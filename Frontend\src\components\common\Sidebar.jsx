import { Link, useLocation, useNavigate } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
import "../../styles/Sidebar.css";
import useModalScrollLock from "../../hooks/useModalScrollLock";
import { MdDashboard } from "react-icons/md";
import { FaUser, FaDownload, FaGavel, FaCreditCard } from "react-icons/fa";
import {
  MdRequestPage,
  MdVideoLibrary,
  MdLocalOffer,
  MdPayment,
} from "react-icons/md";
import { IoLogOut } from "react-icons/io5";
import { useDispatch } from "react-redux";
import { setActiveTab } from "../../redux/slices/buyerDashboardSlice";
import { setActiveTab as setSellerActiveTab } from "../../redux/slices/sellerDashboardSlice";
import { logout } from "../../redux/slices/authSlice";
import logo from "../../assets/images/XOsports-hub-logo.png";
import RoleDropdown from "./RoleDropdown";
import ErrorBoundary from "./ErrorBoundary";
import {
  handleBuyNavigation,
  handleSellNavigation,
} from "../../utils/navigationUtils";
import { showInfo } from "../../utils/toast";
import { usePublicSettings } from "../../hooks/useSettings";
import { needsSellerOnboarding } from "../../utils/sellerUtils";

const Sidebar = ({
  isOpen,
  toggleSidebar,
  userRole,
  effectiveRole,
  userData,
}) => {
  const location = useLocation();
  const path = location.pathname;
  const sidebarRef = useRef(null);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [accountDropdownOpen, setAccountDropdownOpen] = useState(false);
  const [sellerAccountDropdownOpen, setSellerAccountDropdownOpen] =
    useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // Get site settings
  const { siteName, getSiteLogoUrl } = usePublicSettings();

  // Check if seller onboarding is incomplete
  const isSellerOnboardingIncomplete = effectiveRole === "seller" && userRole !== "admin" && needsSellerOnboarding(userData);

  // Use scroll lock only on mobile devices
  useModalScrollLock(isMobile && isOpen);

  // Close sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target) &&
        isOpen
      ) {
        toggleSidebar();
      }
    };

    document.addEventListener("onclick", handleClickOutside);
    return () => {
      document.removeEventListener("onclick", handleClickOutside);
    };
  }, [isOpen, toggleSidebar]);

  // Handle window resize to update mobile state
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Close sidebar when clicking a link
  const handleLinkClick = () => {
    toggleSidebar();
  };

  // Handle disabled navigation link clicks
  const handleDisabledLinkClick = (e, linkName) => {
    e.preventDefault();
    showInfo(`Complete your seller onboarding to access ${linkName}. Only the Seller Form is available until onboarding is complete.`);
  };

  // Handle account navigation
  const handleAccountNavigation = (tab) => {
    if (tab === "logout") {
      // Handle logout logic
      dispatch(logout());
      navigate("/");
    } else {
      dispatch(setActiveTab(tab));
      // Navigate to the corresponding route for buyers
      switch (tab) {
        case "dashboard":
          navigate("/buyer/account/dashboard");
          break;
        case "downloads":
          navigate("/buyer/account/downloads");
          break;
        case "requests":
          navigate("/buyer/account/requests");
          break;
        case "bids":
          navigate("/buyer/account/bids");
          break;
        case "offers":
          navigate("/buyer/account/offers");
          break;
        case "cards":
          navigate("/buyer/account/cards");
          break;
        case "profile":
          navigate("/buyer/account/profile");
          break;
        default:
          navigate("/buyer/account/dashboard");
      }
    }
    toggleSidebar();
  };

  // Handle seller dropdown navigation
  const handleSellerDropdownNavigation = (tab) => {
    dispatch(setSellerActiveTab(tab));
    // Navigate to the corresponding route
    switch (tab) {
      case "dashboard":
        navigate("/seller/dashboard");
        break;
      case "my-sports-strategies":
        navigate("/seller/my-sports-strategies");
        break;
      case "requests":
        navigate("/seller/requests");
        break;
      case "bids":
        navigate("/seller/bids");
        break;
      case "offers":
        navigate("/seller/offers");
        break;
      case "cards":
        navigate("/seller/cards");
        break;
      case "payment-settings":
        navigate("/seller/payment-settings");
        break;
      case "profile":
        navigate("/seller/profile");
        break;
      default:
        navigate("/seller/dashboard");
    }
    toggleSidebar();
  };

  // Handle seller logout
  const handleSellerLogout = () => {
    dispatch(logout());
    navigate("/");
    toggleSidebar();
  };

  // Handle Buy tab click with authentication and role checks
  const handleBuyClick = (e) => {
    e.preventDefault();
    const success = handleBuyNavigation(navigate);
    if (success) {
      toggleSidebar();
    }
  };

  // Handle Sell tab click with authentication and role checks
  const handleSellClick = (e) => {
    e.preventDefault();
    const success = handleSellNavigation(navigate);
    if (success) {
      toggleSidebar();
    }
  };

  return (
    <div
      className={`sidebar-component sidebar-container ${isOpen ? "active" : ""
        }`}
    >
      <div className="sidebar-overlay" onClick={toggleSidebar}></div>
      <div className="sidebar" ref={sidebarRef}>
        <div className="sidebar-header">
          <div className="sidebar-header__logo">
            <Link to="/" onClick={handleLinkClick}>
              <img
                src={getSiteLogoUrl() || logo}
                alt={siteName || "XO Sports Hub"}
              />
            </Link>
          </div>
          {/* Role Dropdown in header for authenticated non-admin users */}
          {userData && userRole !== "admin" && (
            <div className="sidebar-header__role-dropdown">
              <ErrorBoundary
                title="Role Dropdown Error"
                message="There was an issue with the role dropdown. Please refresh the page."
              >
                <RoleDropdown isMobile={true} />
              </ErrorBoundary>
            </div>
          )}
        </div>

        <div className="sidebar-links">
          {effectiveRole === "visitor" && (
            // Visitor links
            <>
              <Link
                to="/"
                className={path === "/" ? "active" : ""}
                onClick={handleLinkClick}
              >
                Home
              </Link>
              <Link
                to="/content"
                className={path === "/content" ? "active" : ""}
                onClick={handleLinkClick}
              >
                Content
              </Link>

              <a
                href="#"
                className={path.startsWith("/buyer") ? "active" : ""}
                onClick={handleBuyClick}
              >
                Buy
              </a>
              <a
                href="#"
                className={path.startsWith("/seller") ? "active" : ""}
                onClick={handleSellClick}
              >
                Sell
              </a>
              <Link
                to="/contact"
                className={path === "/contact" ? "active" : ""}
                onClick={handleLinkClick}
              >
                Contact Us
              </Link>
            </>
          )}

          {effectiveRole === "buyer" && userRole !== "admin" && (
            // Buyer links
            <>
              <Link
                to="/"
                className={path === "/" ? "active" : ""}
                onClick={handleLinkClick}
              >
                Home
              </Link>
              <Link
                to="/content"
                className={path === "/content" ? "active" : ""}
                onClick={handleLinkClick}
              >
                Content
              </Link>
              <a
                href="#"
                className={path.startsWith("/buyer") ? "active" : ""}
                onClick={handleBuyClick}
              >
                Buy
              </a>
              <Link
                to="/contact"
                className={path === "/contact" ? "active" : ""}
                onClick={handleLinkClick}
              >
                Contact Us
              </Link>

              {/* Account dropdown section */}
              <div className="sidebar-account-section">
                <div
                  className="sidebar-account-header"
                  onClick={() => setAccountDropdownOpen(!accountDropdownOpen)}
                >
                  <span>My Account</span>
                  <span
                    className={`sidebar-dropdown-icon ${accountDropdownOpen ? "active" : ""
                      }`}
                  >
                    ▼
                  </span>
                </div>

                {accountDropdownOpen && (
                  <div className="sidebar-account-dropdown">
                    <Link
                      to="/buyer/account/dashboard"
                      className={
                        path.includes("/account/dashboard") ? "active" : ""
                      }
                      onClick={() => handleAccountNavigation("dashboard")}
                    >
                      <MdDashboard className="sidebar-icon" />
                      <span>Dashboard</span>
                    </Link>
                    <Link
                      to="/buyer/account/downloads"
                      className={path.includes("/downloads") ? "active" : ""}
                      onClick={() => handleAccountNavigation("downloads")}
                    >
                      <FaDownload className="sidebar-icon" />
                      <span>My Downloads</span>
                    </Link>

                    <Link
                      to="/buyer/account/bids"
                      className={path.includes("/bids") ? "active" : ""}
                      onClick={() => handleAccountNavigation("bids")}
                    >
                      <FaGavel className="sidebar-icon" />
                      <span>My Bids</span>
                    </Link>

                    <Link
                      to="/buyer/account/offers"
                      className={path.includes("/offers") ? "active" : ""}
                      onClick={() => handleAccountNavigation("offers")}
                    >
                      <MdLocalOffer className="sidebar-icon" />
                      <span>My Offers</span>
                    </Link>

                    <Link
                      to="/buyer/account/requests"
                      className={path.includes("/requests") ? "active" : ""}
                      onClick={() => handleAccountNavigation("requests")}
                    >
                      <MdRequestPage className="sidebar-icon" />
                      <span>Custo Requests</span>
                    </Link>

                    <Link
                      to="/buyer/account/cards"
                      className={path.includes("/cards") ? "active" : ""}
                      onClick={() => handleAccountNavigation("cards")}
                    >
                      <FaCreditCard className="sidebar-icon" />
                      <span>My Cards</span>
                    </Link>
                    <Link
                      to="/buyer/account/profile"
                      className={path.includes("/profile") ? "active" : ""}
                      onClick={() => handleAccountNavigation("profile")}
                    >
                      <FaUser className="sidebar-icon" />
                      <span>My Profile</span>
                    </Link>
                  </div>
                )}
              </div>
            </>
          )}

          {effectiveRole === "seller" && userRole !== "admin" && (
            // Seller links
            <>
              <Link
                to="/"
                className={`${path === "/" ? "active" : ""} ${isSellerOnboardingIncomplete ? "disabled" : ""}`}
                onClick={isSellerOnboardingIncomplete ? (e) => handleDisabledLinkClick(e, "Home") : handleLinkClick}
              >
                Home
              </Link>
              <Link
                to="/content"
                className={`${path === "/content" ? "active" : ""} ${isSellerOnboardingIncomplete ? "disabled" : ""}`}
                onClick={(e) => {
                  if (isSellerOnboardingIncomplete) {
                    handleDisabledLinkClick(e, "Content");
                  } else {
                    e.preventDefault();
                    showInfo("Switch to Buyer mode to move content pages.");
                  }
                }}
              >
                Content
              </Link>
              <a
                href="#"
                className={path.startsWith("/seller") ? "active" : ""}
                onClick={handleSellClick}
              >
                Sell
              </a>
              <Link
                to="/contact"
                className={`${path === "/contact" ? "active" : ""} ${isSellerOnboardingIncomplete ? "disabled" : ""}`}
                onClick={isSellerOnboardingIncomplete ? (e) => handleDisabledLinkClick(e, "Contact Us") : handleLinkClick}
              >
                Contact Us
              </Link>
              {/* Account dropdown section */}
              <div className="sidebar-account-section">
                <div
                  className={`sidebar-account-header ${isSellerOnboardingIncomplete ? "disabled" : ""}`}
                  onClick={() => {
                    if (isSellerOnboardingIncomplete) {
                      handleDisabledLinkClick(null, "My Account");
                    } else {
                      setSellerAccountDropdownOpen(!sellerAccountDropdownOpen);
                    }
                  }}
                >
                  <span>My Account</span>
                  <span
                    className={`sidebar-dropdown-icon ${sellerAccountDropdownOpen ? "active" : ""}`}
                  >
                    ▼
                  </span>
                </div>

                {sellerAccountDropdownOpen && (
                  <div className="sidebar-account-dropdown">
                    <Link
                      to="/seller/dashboard"
                      className={path === "/seller/dashboard" ? "active" : ""}
                      onClick={() =>
                        handleSellerDropdownNavigation("dashboard")
                      }
                    >
                      <MdDashboard className="sidebar-icon" />
                      <span>Dashboard</span>
                    </Link>

                    <Link
                      to="/seller/my-sports-strategies"
                      className={
                        path === "/seller/my-sports-strategies" ? "active" : ""
                      }
                      onClick={() =>
                        handleSellerDropdownNavigation("my-sports-strategies")
                      }
                    >
                      <MdVideoLibrary className="sidebar-icon" />
                      <span>My Sports Strategies</span>
                    </Link>

                    <Link
                      to="/seller/requests"
                      className={path === "/seller/requests" ? "active" : ""}
                      onClick={() => handleSellerDropdownNavigation("requests")}
                    >
                      <MdRequestPage className="sidebar-icon" />
                      <span>Custom Requests</span>
                    </Link>

                    <Link
                      to="/seller/bids"
                      className={path === "/seller/bids" ? "active" : ""}
                      onClick={() => handleSellerDropdownNavigation("bids")}
                    >
                      <FaGavel className="sidebar-icon" />
                      <span>My Bids</span>
                    </Link>

                    <Link
                      to="/seller/offers"
                      className={path === "/seller/offers" ? "active" : ""}
                      onClick={() => handleSellerDropdownNavigation("offers")}
                    >
                      <MdLocalOffer className="sidebar-icon" />
                      <span>My Offers</span>
                    </Link>

                    {/* <Link
                      to="/seller/cards"
                      className={path === "/seller/cards" ? "active" : ""}
                      onClick={() => handleSellerDropdownNavigation("cards")}
                    >
                      <FaCreditCard className="sidebar-icon" />
                      <span>My Cards</span>
                    </Link> */}

                    <Link
                      to="/seller/payment-settings"
                      className={
                        path === "/seller/payment-settings" ? "active" : ""
                      }
                      onClick={() =>
                        handleSellerDropdownNavigation("payment-settings")
                      }
                    >
                      <MdPayment className="sidebar-icon" />
                      <span>Payment Settings</span>
                    </Link>
                    <Link
                      to="/seller/profile"
                      className={path === "/seller/profile" ? "active" : ""}
                      onClick={() => handleSellerDropdownNavigation("profile")}
                    >
                      <FaUser className="sidebar-icon" />
                      <span>My Profile</span>
                    </Link>
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        <div className=" sidebtnset">
          <div className="sidebar-auth">
            {effectiveRole === "visitor" && (
              <>
                <Link
                  to="/auth"
                  className="btn signinbtn"
                  onClick={handleLinkClick}
                >
                  Sign In
                </Link>
                <Link
                  to="/signup"
                  className="btn signupbtn"
                  onClick={handleLinkClick}
                >
                  Sign Up
                </Link>
              </>
            )}

            {effectiveRole === "seller" && userRole !== "admin" && (
              <button className="btn btn-outline" onClick={handleSellerLogout}>
                <IoLogOut style={{ marginRight: "8px" }} />
                Logout
              </button>
            )}

            {effectiveRole === "buyer" && userRole !== "admin" && (
              <Link
                to="/"
                className="btn btn-outline"
                onClick={() => handleAccountNavigation("logout")}
              >
                <IoLogOut style={{ marginRight: "8px" }} />
                Logout
              </Link>
            )}

            {userRole === "admin" && (
              <button className="btn btn-outline" onClick={handleSellerLogout}>
                <IoLogOut style={{ marginRight: "8px" }} />
                Logout
              </button>
            )}
          </div>

        </div>
      </div>
    </div>
  );
};

export default Sidebar;
