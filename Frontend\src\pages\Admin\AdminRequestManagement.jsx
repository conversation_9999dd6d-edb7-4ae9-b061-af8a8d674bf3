import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
    selectRequests,
    selectSelectedRequests,
    selectLoading,
    selectRequestFilters,
    selectRequestsPagination,
    setSelectedRequests,
    setRequestFilters,
    showRequestDetailModal,
    addActivity,
} from "../../redux/slices/adminDashboardSlice";
import {
    fetchRequests,
    updateRequestStatus,
    deleteRequest,
    bulkUpdateRequests,
    bulkDeleteRequests,
} from "../../redux/slices/adminDashboardThunks";
import AdminLayout from "../../components/admin/AdminLayout";
import Table from "../../components/common/Table";
import AdminPagination from "../../components/admin/AdminPagination";
import AdminTableActions from "../../components/admin/AdminTableActions";
import RequestDetailModal from "../../components/admin/RequestDetailModal";
import "../../styles/AdminRequestManagement.css";
import { FaSearch, FaEye, FaTrash, FaCheck, FaTimes, FaCalendarAlt, FaUser, FaFileAlt } from "react-icons/fa";
import { getSmartFileUrl, getPlaceholderImage } from "../../utils/constants";

const AdminRequestManagement = () => {
    const dispatch = useDispatch();

    // Redux state
    const requests = useSelector(selectRequests);
    const selectedRequests = useSelector(selectSelectedRequests);
    const loading = useSelector(selectLoading);
    const filters = useSelector(selectRequestFilters);
    const pagination = useSelector(selectRequestsPagination);

    // Local state for immediate UI updates
    const [localFilters, setLocalFilters] = useState({
        search: '',
        status: '',
        sport: '',
        contentType: ''
    });

    // Load requests on component mount and when filters change
    useEffect(() => {
        const params = {
            page: pagination.current,
            limit: pagination.limit,
            ...filters
        };
        dispatch(fetchRequests(params));
    }, [dispatch, filters, pagination.current, pagination.limit]);

    // Handle filter changes
    const handleFilterChange = (key, value) => {
        setLocalFilters(prev => ({ ...prev, [key]: value }));

        // Debounce search, immediate for others
        if (key === 'search') {
            const timeoutId = setTimeout(() => {
                dispatch(setRequestFilters({ [key]: value, page: 1 }));
            }, 500);
            return () => clearTimeout(timeoutId);
        } else {
            dispatch(setRequestFilters({ [key]: value, page: 1 }));
        }
    };

    // Handle page change
    const handlePageChange = (page) => {
        dispatch(setRequestFilters({ page }));
    };

    // Handle request actions
    const handleRequestAction = async (action, requestItem) => {
        const requestId = requestItem._id;
        const displayRequestId = requestId.slice(-8).toUpperCase();
        const requestTitle = requestItem.title;

        switch (action) {
            case "view":
            case "edit":
                dispatch(showRequestDetailModal(requestItem));
                break;
            case "approve":
                if (window.confirm(`Accept request "${displayRequestId}"?`)) {
                    try {
                        await dispatch(
                            updateRequestStatus({ id: requestId, status: "Accepted", notes: "" })
                        ).unwrap();
                        dispatch(
                            addActivity({
                                id: Date.now(),
                                type: "request_acceptance",
                                description: `Request accepted: ${displayRequestId} - ${requestTitle}`,
                                timestamp: new Date().toISOString(),
                                user: "Admin",
                            })
                        );
                        alert(`Request "${displayRequestId}" has been accepted!`);
                    } catch (error) {
                        alert(`Failed to accept request: ${error}`);
                    }
                }
                break;
            case "reject":
                if (window.confirm(`Reject request "${displayRequestId}"?`)) {
                    try {
                        await dispatch(
                            updateRequestStatus({ id: requestId, status: "Rejected", notes: "" })
                        ).unwrap();
                        dispatch(
                            addActivity({
                                id: Date.now(),
                                type: "request_rejection",
                                description: `Request rejected: ${displayRequestId} - ${requestTitle}`,
                                timestamp: new Date().toISOString(),
                                user: "Admin",
                            })
                        );
                        alert(`Request "${displayRequestId}" has been rejected!`);
                    } catch (error) {
                        alert(`Failed to reject request: ${error}`);
                    }
                }
                break;
            case "delete":
                if (window.confirm(`Delete request "${displayRequestId}"? This action cannot be undone.`)) {
                    try {
                        await dispatch(deleteRequest(requestId)).unwrap();
                        dispatch(
                            addActivity({
                                id: Date.now(),
                                type: "request_deletion",
                                description: `Request deleted: ${displayRequestId} - ${requestTitle}`,
                                timestamp: new Date().toISOString(),
                                user: "Admin",
                            })
                        );
                        alert(`Request "${displayRequestId}" has been deleted!`);
                    } catch (error) {
                        alert(`Failed to delete request: ${error}`);
                    }
                }
                break;
            default:
                console.warn(`Unknown action: ${action}`);
        }
    };

    // Handle bulk actions
    const handleBulkAction = async (action) => {
        if (selectedRequests.length === 0) {
            alert("Please select requests to perform bulk actions.");
            return;
        }

        const requestIds = selectedRequests.map(req => req._id);
        const confirmMessage = `${action} ${selectedRequests.length} selected request(s)?`;

        if (window.confirm(confirmMessage)) {
            try {
                switch (action) {
                    case "Accept":
                        await dispatch(bulkUpdateRequests({ requestIds, status: "Accepted" })).unwrap();
                        break;
                    case "Reject":
                        await dispatch(bulkUpdateRequests({ requestIds, status: "Rejected" })).unwrap();
                        break;
                    case "Delete":
                        await dispatch(bulkDeleteRequests(requestIds)).unwrap();
                        break;
                    default:
                        return;
                }

                dispatch(setSelectedRequests([]));
                dispatch(
                    addActivity({
                        id: Date.now(),
                        type: "bulk_request_action",
                        description: `Bulk ${action.toLowerCase()}: ${selectedRequests.length} requests`,
                        timestamp: new Date().toISOString(),
                        user: "Admin",
                    })
                );
                alert(`Successfully ${action.toLowerCase()}ed ${selectedRequests.length} request(s)!`);
            } catch (error) {
                alert(`Failed to ${action.toLowerCase()} requests: ${error}`);
            }
        }
    };

    // Table columns configuration
    const columns = [
        {
            key: "requestId",
            label: "Request ID",
            render: (request) => (
                <span className="request-id">
                    #{request._id?.slice(-8).toUpperCase() || 'N/A'}
                </span>
            ),
        },
        {
            key: "title",
            label: "Title & Content Type",
            render: (request) => (
                <div className="request-info">
                    <div className="request-title">{request.title || 'Untitled'}</div>
                    <div className="request-content-type">
                        <span className="content-type-badge">{request.contentType || 'N/A'}</span>
                    </div>
                </div>
            ),
        },
        {
            key: "buyer",
            label: "Buyer",
            render: (request) => (
                <div className="user-info">
                    <div className="user-avatar">
                        {request.buyer?.profileImage ? (
                            <img
                                src={getSmartFileUrl(request.buyer.profileImage)}
                                alt={request.buyer.firstName}
                                onError={(e) => {
                                    e.target.src = getPlaceholderImage(32, 32, "U");
                                }}
                            />
                        ) : (
                            <div className="avatar-placeholder">
                                <FaUser />
                            </div>
                        )}
                    </div>
                    <div className="user-details">
                        <span className="name">
                            {request.buyer?.firstName && request.buyer?.lastName
                                ? `${request.buyer.firstName} ${request.buyer.lastName}`
                                : 'Unknown User'}
                        </span>
                        <span className="email">{request.buyer?.email || 'No email'}</span>
                    </div>
                </div>
            ),
        },
        {
            key: "sport",
            label: "Sport",
            render: (request) => (
                <span className="sport-badge">{request.sport || 'N/A'}</span>
            ),
        },
        {
            key: "budget",
            label: "Budget",
            render: (request) => (
                <div className="budget-info">
                    <span>${request.budget?.toFixed(2) || '0.00'}</span>
                </div>
            ),
        },
        {
            key: "status",
            label: "Status",
            render: (request) => (
                <span className={`status-badge status-${request.status?.toLowerCase() || 'unknown'}`}>
                    {request.status || 'Unknown'}
                </span>
            ),
        },
        {
            key: "createdAt",
            label: "Created Date",
            render: (request) => (
                <div className="date-info">
                    <FaCalendarAlt className="icon" />
                    <span>
                        {request.createdAt
                            ? new Date(request.createdAt).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                            })
                            : 'N/A'}
                    </span>
                </div>
            ),
        },
        {
            key: "actions",
            label: "Actions",
            render: (request) => (
                <div className="action-buttons">
                    <button
                        className="btn btn-sm btn-primary"
                        onClick={() => handleRequestAction("view", request)}
                        title="View Details"
                    >
                        <FaEye />
                    </button>
                    {request.status === 'Pending' && (
                        <>
                            <button
                                className="btn btn-sm btn-success"
                                onClick={() => handleRequestAction("approve", request)}
                                title="Accept Request"
                            >
                                <FaCheck />
                            </button>
                            <button
                                className="btn btn-sm btn-danger"
                                onClick={() => handleRequestAction("reject", request)}
                                title="Reject Request"
                            >
                                <FaTimes />
                            </button>
                        </>
                    )}
                </div>
            ),
        },
    ];

    // Bulk action options
    const bulkActions = [
        { label: "Accept Selected", action: "Accept" },
        { label: "Reject Selected", action: "Reject" },
        { label: "Delete Selected", action: "Delete" },
    ];

    return (
        <AdminLayout>
            <div className="AdminRequestManagement">
                <div className="AdminRequestManagement__header">
                    <h1>Request Management</h1>
                    <p>Manage and monitor all custom training requests</p>
                </div>

                {/* Filters and Search */}
                <div className="AdminRequestManagement__controls">
                    <div className="search-box">
                        <FaSearch className="search-icon" />
                        <input
                            type="text"
                            placeholder="Search requests..."
                            value={localFilters.search}
                            onChange={(e) => handleFilterChange('search', e.target.value)}
                        />
                    </div>

                    <div className="filter-box">
                        <select
                            value={localFilters.status}
                            onChange={(e) => handleFilterChange('status', e.target.value)}
                        >
                            <option value="">All Status</option>
                            <option value="Pending">Pending</option>
                            <option value="Accepted">Accepted</option>
                            <option value="Rejected">Rejected</option>
                            <option value="In Progress">In Progress</option>
                            <option value="Completed">Completed</option>
                            <option value="Cancelled">Cancelled</option>
                        </select>
                        <select
                            value={localFilters.sport}
                            onChange={(e) => handleFilterChange('sport', e.target.value)}
                        >
                            <option value="">All Sports</option>
                            <option value="Basketball">Basketball</option>
                            <option value="Football">Football</option>
                            <option value="Soccer">Soccer</option>
                            <option value="Baseball">Baseball</option>
                        </select>
                        <select
                            value={localFilters.contentType}
                            onChange={(e) => handleFilterChange('contentType', e.target.value)}
                        >
                            <option value="">All Content Types</option>
                            <option value="Video">Video</option>
                            <option value="PDF">PDF</option>
                        </select>
                    </div>
                </div>

                {/* Bulk Actions */}
                <AdminTableActions
                    selectedItems={selectedRequests}
                    bulkActions={bulkActions}
                    onBulkAction={handleBulkAction}
                    onSelectAll={(selectAll) => {
                        if (selectAll) {
                            dispatch(setSelectedRequests(requests.data));
                        } else {
                            dispatch(setSelectedRequests([]));
                        }
                    }}
                    totalItems={requests.data?.length || 0}
                />

                {/* Table */}
                <Table
                    columns={columns}
                    data={requests.data || []}
                    isAdmin={true}
                    loading={{
                        isLoading: loading.requests,
                        message: "Loading requests...",
                    }}
                    emptyMessage={
                        <div className="no-results">
                            <FaFileAlt className="no-results-icon" />
                            <h3>No requests found</h3>
                            <p>Try adjusting your search or filter criteria</p>
                        </div>
                    }
                    className="requests-table"
                />

                {/* Pagination */}
                <AdminPagination
                    currentPage={pagination.current}
                    totalPages={pagination.pages}
                    totalItems={pagination.total}
                    itemsPerPage={pagination.limit}
                    onPageChange={handlePageChange}
                />

                {/* Request Detail Modal */}
                <RequestDetailModal />
            </div>
        </AdminLayout>
    );
};

export default AdminRequestManagement;