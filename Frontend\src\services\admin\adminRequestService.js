import axios from 'axios';
import { STORAGE_KEYS } from '../../utils/constants';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_URL}/admin/requests`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Get all custom requests with filtering, sorting, and pagination
export const getAllRequests = async (params = {}) => {
  try {
    const response = await api.get('/', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get request by ID
export const getRequestById = async (id) => {
  try {
    const response = await api.get(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update request status
export const updateRequestStatus = async (id, status, notes = '') => {
  try {
    const response = await api.put(`/${id}/status`, { status, notes });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Delete request
export const deleteRequest = async (id) => {
  try {
    const response = await api.delete(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk update requests
export const bulkUpdateRequests = async (requestIds, status) => {
  try {
    const response = await api.post('/bulk-update', { requestIds, status });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Bulk delete requests
export const bulkDeleteRequests = async (requestIds) => {
  try {
    const response = await api.post('/bulk-delete', { requestIds });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Flag request
export const flagRequest = async (id, reason) => {
  try {
    const response = await api.put(`/${id}/flag`, { reason });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Unflag request
export const unflagRequest = async (id) => {
  try {
    const response = await api.put(`/${id}/unflag`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Moderate request (approve, reject, flag)
export const moderateRequest = async (id, action, reason = '') => {
  try {
    const response = await api.put(`/${id}/moderate`, { action, reason });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get request statistics
export const getRequestStats = async () => {
  try {
    const response = await api.get('/stats');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get request analytics
export const getRequestAnalytics = async (params = {}) => {
  try {
    const response = await api.get('/analytics', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get request timeline
export const getRequestTimeline = async (id) => {
  try {
    const response = await api.get(`/${id}/timeline`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Export requests
export const exportRequests = async (params = {}) => {
  try {
    const response = await api.get('/export', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Request management actions
export const requestActions = {
  approve: (id) => moderateRequest(id, 'approve'),
  reject: (id, reason) => moderateRequest(id, 'reject', reason),
  flag: (id, reason) => flagRequest(id, reason),
  unflag: (id) => unflagRequest(id),
  delete: (id) => deleteRequest(id)
};

export default {
  getAllRequests,
  getRequestById,
  updateRequestStatus,
  deleteRequest,
  bulkUpdateRequests,
  bulkDeleteRequests,
  flagRequest,
  unflagRequest,
  moderateRequest,
  getRequestStats,
  getRequestAnalytics,
  getRequestTimeline,
  exportRequests,
  requestActions
};
